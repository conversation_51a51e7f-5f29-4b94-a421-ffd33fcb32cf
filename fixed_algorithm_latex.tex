% 修正版算法代码 - 解决Overleaf编译问题
% 确保在文档头部有正确的包

% 方案1: 使用algorithm2e包（推荐）
\begin{algorithm}[htbp]
\caption{Subgraph GAT for Interference Identification}
\label{alg:subgraph_gat}
\SetKwInOut{Input}{Input}
\SetKwInOut{Output}{Output}
\Input{$G = (V, E)$, lightpaths $P_{new}, P_{target}$}
\Output{Interference prediction $\{0, 1\}$}

$V_{rel} \leftarrow \text{nodes}(P_{new} \cup P_{target})$\;
\ForEach{$v \in V_{rel}$}{
    $V_{rel} \leftarrow V_{rel} \cup N_1(v)[1:3]$ \tcp{Add 1-hop neighbors}
}
$G_{sub} \leftarrow$ induced subgraph on $V_{rel}$\;

\ForEach{$v \in V_{rel}$}{
    $F[v] \leftarrow [I_{src/dst}(v), \text{power}(v), \text{degree}(v), I_{path}(v)]$\;
}

$H^{(1)} \leftarrow \text{GAT}(F, G_{sub})$\;
$H^{(2)} \leftarrow \text{GAT}(H^{(1)}, G_{sub})$\;
$h_{graph} \leftarrow \text{MeanPool}(H^{(2)})$\;
$\text{pred} \leftarrow \text{MLP}(h_{graph})$\;
\Return{$\arg\max(\text{pred})$}
\end{algorithm}