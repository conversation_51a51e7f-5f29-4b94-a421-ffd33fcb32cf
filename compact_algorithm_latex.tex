% 简洁美观的算法伪代码 - LaTeX版本
% 需要在文档头部添加: \usepackage{algorithm} \usepackage{algorithmic}

\begin{algorithm}[htbp]
\caption{Subgraph GAT for Lightpath Interference Identification}
\label{alg:subgraph_gat}
\begin{algorithmic}[1]
\REQUIRE Network $G = (V, E)$, new lightpath $P_{new}$, target lightpath $P_{target}$
\ENSURE Binary interference prediction $\{0, 1\}$

\STATE \textbf{Phase 1: Subgraph Construction}
\STATE $V_{rel} \leftarrow \text{nodes}(P_{new}) \cup \text{nodes}(P_{target})$
\FOR{each $v \in V_{rel}$}
    \STATE $N_v \leftarrow$ 1-hop neighbors of $v$ in $G$
    \STATE $V_{rel} \leftarrow V_{rel} \cup N_v[1:3]$ \COMMENT{Add up to 3 neighbors}
\ENDFOR
\STATE $G_{sub} \leftarrow$ induced subgraph of $G$ on $V_{rel}$

\STATE \textbf{Phase 2: Feature Engineering}  
\FOR{each $v \in V_{rel}$}
    \STATE $f_v \leftarrow [I_{src}(v,P_{new}), I_{dst}(v,P_{new}), I_{src}(v,P_{target}),$
    \STATE \hspace{1.5cm} $I_{dst}(v,P_{target}), \text{power}(v), \text{degree}(v), I_{path}(v)]$
\ENDFOR
\STATE $F \leftarrow$ feature matrix $\in \mathbb{R}^{|V_{rel}| \times 7}$

\STATE \textbf{Phase 3: GAT Classification}
\STATE $H^{(1)} \leftarrow \text{MultiHeadGAT}(F, G_{sub}, \text{heads}=4, \text{dim}=32)$
\STATE $H^{(2)} \leftarrow \text{MultiHeadGAT}(H^{(1)}, G_{sub}, \text{heads}=4, \text{dim}=32)$
\STATE $h_{graph} \leftarrow \text{MeanPooling}(H^{(2)})$ \COMMENT{Graph representation}
\STATE $\text{logits} \leftarrow \text{MLP}(h_{graph})$ \COMMENT{128$\rightarrow$32$\rightarrow$2}
\RETURN $\arg\max(\text{Softmax}(\text{logits}))$
\end{algorithmic}
\end{algorithm}