\documentclass[border=10pt]{standalone}
\usepackage{amsmath}

\begin{document}

\begin{minipage}{4in}
\centering
\textbf{Algorithm 1: Subgraph GAT for Interference Identification}

\vspace{0.3cm}
\begin{flushleft}
\textbf{Input:} $G = (V, E)$, lightpaths $P_{new}, P_{target}$ \\
\textbf{Output:} Interference prediction $\{0, 1\}$

\vspace{0.2cm}
\textbf{1:} $V_{rel} \leftarrow \text{nodes}(P_{new} \cup P_{target})$ \\
\textbf{2:} \textbf{for} each $v \in V_{rel}$ \textbf{do} \\
\textbf{3:} \quad $V_{rel} \leftarrow V_{rel} \cup \text{neighbors}(v)[1:3]$ \\
\textbf{4:} \textbf{end for} \\
\textbf{5:} $G_{sub} \leftarrow$ induced subgraph on $V_{rel}$ \\

\vspace{0.1cm}
\textbf{6:} \textbf{for} each $v \in V_{rel}$ \textbf{do} \\
\textbf{7:} \quad $F[v] \leftarrow \text{generate\_features}(v, P_{new}, P_{target})$ \\
\textbf{8:} \textbf{end for} \\

\vspace{0.1cm}
\textbf{9:} $H^{(1)} \leftarrow \text{GAT}(F, G_{sub})$ \\
\textbf{10:} $H^{(2)} \leftarrow \text{GAT}(H^{(1)}, G_{sub})$ \\
\textbf{11:} $h \leftarrow \text{MeanPool}(H^{(2)})$ \\
\textbf{12:} $pred \leftarrow \text{MLP}(h)$ \\
\textbf{13:} \textbf{return} $\arg\max(pred)$
\end{flushleft}
\end{minipage}

\end{document}