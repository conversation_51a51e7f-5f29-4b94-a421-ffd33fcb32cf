# 研究点二：基于深度强化学习的QoT裕量感知资源分配

---

## 🔴 研究背景：

在研究点一实现快速QoT影响预测的基础上，光网络面临着更为复杂的资源分配决策挑战。传统的资源分配策略采用固定的QoT裕量分配方式，无法根据网络动态状态和业务需求进行智能调整，导致网络资源利用率低下。同时，光网络中的路径选择、波长分配和功率控制三个维度相互耦合，形成了高维度的多约束优化问题，传统的启发式算法难以在保证QoT的前提下实现全局最优。此外，现代光网络需要在毫秒级时间内响应动态业务请求，这要求资源分配算法既要保证决策质量，又要满足实时性要求，传统的数学规划方法计算复杂度过高，无法满足实际应用需求。

---

## 🔴 研究问题：

如何在光网络动态环境下，基于研究点一提供的QoT影响预测能力，设计一种QoT裕量感知的智能资源分配策略，实现路径选择、波长分配、功率控制的联合优化，在保障全网QoT安全域的前提下最大化网络容量利用率？具体而言，需要解决三个核心问题：一是如何根据不同业务的优先级和QoT要求动态分配合适的QoT裕量，避免过度保守造成的资源浪费或裕量不足导致的服务质量下降；二是如何处理多维度资源约束的耦合效应，在路径-波长-功率的三维空间中找到帕累托最优解；三是如何在网络状态持续变化的环境下实现实时决策与长期优化的平衡，既要快速响应新业务请求，又要维护网络的长期性能稳定。

---

## 🔴 研究方案：

基于深度强化学习框架，设计QoT裕量感知的分层资源分配策略。首先构建多维状态空间表示，将网络拓扑、光路状态、QoT裕量分布和资源占用情况统一建模，结合研究点一的子图GAT预测结果作为状态输入的重要组成部分。然后采用Actor-Critic架构设计智能决策网络，其中Actor网络负责输出资源分配动作（路径选择、波长分配、功率控制），Critic网络评估动作的长期价值，通过多目标奖励函数平衡QoT保障、频谱效率和功率消耗三个优化目标。为了处理多时间尺度的决策需求，设计分层决策机制：快速响应层处理毫秒级的功率微调和波长切换，中期调整层负责分钟级的路径重路由和负载均衡，长期规划层进行小时级的网络拓扑优化。通过经验回放机制和在线学习算法，使系统能够从历史决策中不断学习，适应网络状态的动态变化，最终实现QoT约束下的网络容量最大化。
