% 解决方案1：使用自然大小，居中显示
\begin{figure*}[htbp]
\centering
\includegraphics{Fig2_algorithm.pdf}  % 不指定宽度，使用原始大小
\caption{Algorithm pseudocode for the complete subgraph GAT procedure.}
\label{fig:algorithm}
\end{figure*}

% 解决方案2：指定合适的宽度比例
\begin{figure*}[htbp]
\centering
\includegraphics[width=0.7\textwidth]{Fig2_algorithm.pdf}  % 使用70%宽度
\caption{Algorithm pseudocode for the complete subgraph GAT procedure.}
\label{fig:algorithm}
\end{figure*}

% 解决方案3：使用单栏图（如果算法图不大）
\begin{figure}[htbp]
\centering
\includegraphics[width=\columnwidth]{Fig2_algorithm.pdf}  % 单栏宽度
\caption{Algorithm pseudocode for the complete subgraph GAT procedure.}
\label{fig:algorithm}
\end{figure}

% 解决方案4：指定具体尺寸
\begin{figure*}[htbp]
\centering
\includegraphics[height=4cm]{Fig2_algorithm.pdf}  % 指定高度而不是宽度
\caption{Algorithm pseudocode for the complete subgraph GAT procedure.}
\label{fig:algorithm}
\end{figure*}

% 解决方案5：使用缩放比例
\begin{figure*}[htbp]
\centering
\includegraphics[scale=0.8]{Fig2_algorithm.pdf}  % 缩放到80%
\caption{Algorithm pseudocode for the complete subgraph GAT procedure.}
\label{fig:algorithm}
\end{figure*}