\documentclass{standalone}
\usepackage{amsmath}
\usepackage{array}

\begin{document}

\begin{tabular}{|p{4in}|}
\hline
\textbf{Algorithm 1:} Subgraph GAT for Interference Identification \\
\hline
\textbf{Input:} $G = (V, E)$, lightpaths $P_{new}, P_{target}$ \\
\textbf{Output:} Interference prediction $\{0, 1\}$ \\
\\
1: $V_{rel} \gets \text{nodes}(P_{new} \cup P_{target})$ \\
2: \textbf{for} each $v \in V_{rel}$ \textbf{do} \\
3: \quad $V_{rel} \gets V_{rel} \cup \text{neighbors}(v)[1:3]$ \\
4: \textbf{end for} \\
5: $G_{sub} \gets$ induced subgraph on $V_{rel}$ \\
\\
6: \textbf{for} each $v \in V_{rel}$ \textbf{do} \\
7: \quad $F[v] \gets \text{generate\_features}(v, P_{new}, P_{target})$ \\
8: \textbf{end for} \\
\\
9: $H^{(1)} \gets \text{GAT}(F, G_{sub})$ \\
10: $H^{(2)} \gets \text{GAT}(H^{(1)}, G_{sub})$ \\
11: $h \gets \text{MeanPool}(H^{(2)})$ \\
12: $pred \gets \text{MLP}(h)$ \\
13: \textbf{return} $\arg\max(pred)$ \\
\hline
\end{tabular}

\end{document}