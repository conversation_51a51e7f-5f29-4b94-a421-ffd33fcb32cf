% 简单版本 - 不依赖特殊算法包
% 使用标准的algorithmic包

\begin{algorithm}[htbp]
\caption{Subgraph GAT for Interference Identification}
\label{alg:subgraph_gat}
\begin{algorithmic}[1]
\REQUIRE $G = (V, E)$, lightpaths $P_{new}, P_{target}$
\ENSURE Interference prediction $\{0, 1\}$

\STATE $V_{rel} \gets \text{nodes}(P_{new} \cup P_{target})$
\FOR{each $v \in V_{rel}$}
    \STATE $V_{rel} \gets V_{rel} \cup N_1(v)[1:3]$ 
\ENDFOR
\STATE $G_{sub} \gets$ induced subgraph on $V_{rel}$

\FOR{each $v \in V_{rel}$}
    \STATE $F[v] \gets [I_{src/dst}(v), \text{power}(v), \text{degree}(v), I_{path}(v)]$
\ENDFOR

\STATE $H^{(1)} \gets \text{GAT}(F, G_{sub})$
\STATE $H^{(2)} \gets \text{GAT}(H^{(1)}, G_{sub})$
\STATE $h_{graph} \gets \text{MeanPool}(H^{(2)})$
\STATE $\text{pred} \gets \text{MLP}(h_{graph})$
\RETURN $\arg\max(\text{pred})$
\end{algorithmic}
\end{algorithm}