\documentclass[crop,border=2pt]{standalone}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}

% 算法命令重定义
\renewcommand{\algorithmicrequire}{\textbf{Input:}}
\renewcommand{\algorithmicensure}{\textbf{Output:}}

% 减少算法内部间距
\setlength{\algorithmicindent}{1em}

\begin{document}

\begin{algorithm}
\caption{Subgraph GAT for Interference Identification}
\begin{algorithmic}[1]
\REQUIRE $G = (V, E)$, lightpaths $P_{new}, P_{target}$
\ENSURE Interference prediction $\{0, 1\}$

\STATE $V_{rel} \gets \text{nodes}(P_{new} \cup P_{target})$
\FOR{each $v \in V_{rel}$}
    \STATE $V_{rel} \gets V_{rel} \cup \text{neighbors}(v)[1:3]$ 
\ENDFOR
\STATE $G_{sub} \gets$ induced subgraph on $V_{rel}$
\FOR{each $v \in V_{rel}$}
    \STATE $F[v] \gets \text{generate\_features}(v, P_{new}, P_{target})$
\ENDFOR
\STATE $H^{(1)} \gets \text{GAT}(F, G_{sub})$, $H^{(2)} \gets \text{GAT}(H^{(1)}, G_{sub})$
\STATE $h \gets \text{MeanPool}(H^{(2)})$, $pred \gets \text{MLP}(h)$
\RETURN $\arg\max(pred)$
\end{algorithmic}
\end{algorithm}

\end{document}