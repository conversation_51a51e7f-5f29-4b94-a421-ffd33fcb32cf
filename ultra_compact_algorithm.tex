% 超紧凑版本算法 - 适合会议论文篇幅限制

\begin{algorithm}[htbp]
\caption{Subgraph GAT for Interference Identification}
\label{alg:subgraph_gat}
\begin{algorithmic}[1]
\REQUIRE $G = (V, E)$, lightpaths $P_{new}, P_{target}$
\ENSURE Interference prediction $\{0, 1\}$

\STATE $V_{rel} \leftarrow \text{nodes}(P_{new} \cup P_{target})$
\STATE Add 1-hop neighbors: $V_{rel} \leftarrow V_{rel} \cup \bigcup_{v \in V_{rel}} N_1(v)[1:3]$
\STATE $G_{sub} \leftarrow$ induced subgraph on $V_{rel}$

\STATE Generate 7D features: $F[v] \leftarrow [I_{src/dst}(v), \text{power}(v), \text{degree}(v), I_{path}(v)]$

\STATE $H^{(1)} \leftarrow \text{GAT}(F, G_{sub})$, $H^{(2)} \leftarrow \text{GAT}(H^{(1)}, G_{sub})$
\STATE $h_{graph} \leftarrow \text{MeanPool}(H^{(2)})$, $\text{pred} \leftarrow \text{MLP}(h_{graph})$
\RETURN $\arg\max(\text{pred})$
\end{algorithmic}
\end{algorithm}