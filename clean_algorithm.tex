\documentclass[border=5pt]{standalone}
\usepackage{amsmath}
\usepackage{amssymb}

\begin{document}

\begin{minipage}{3.5in}
\centering
\textbf{Algorithm 1: Subgraph GAT for Interference Identification}

\vspace{0.2cm}
\begin{flushleft}
\textbf{Input:} $G = (V, E)$, lightpaths $P_{\text{new}}, P_{\text{target}}$ \\
\textbf{Output:} Interference prediction $\{0, 1\}$

\vspace{0.2cm}
\textbf{1:} $V_{\text{rel}} \leftarrow \text{nodes}(P_{\text{new}} \cup P_{\text{target}})$ \\
\textbf{2:} \textbf{for} each $v \in V_{\text{rel}}$ \textbf{do} \\
\textbf{3:} \quad $V_{\text{rel}} \leftarrow V_{\text{rel}} \cup \text{neighbors}(v)[1:3]$ \\
\textbf{4:} \textbf{end for} \\
\textbf{5:} $G_{\text{sub}} \leftarrow$ induced subgraph on $V_{\text{rel}}$ \\
\textbf{6:} \textbf{for} each $v \in V_{\text{rel}}$ \textbf{do} \\
\textbf{7:} \quad $F[v] \leftarrow \text{generate\_features}(v, P_{\text{new}}, P_{\text{target}})$ \\
\textbf{8:} \textbf{end for} \\
\textbf{9:} $H^{(1)} \leftarrow \text{GAT}(F, G_{\text{sub}})$ \\
\textbf{10:} $H^{(2)} \leftarrow \text{GAT}(H^{(1)}, G_{\text{sub}})$ \\
\textbf{11:} $h \leftarrow \text{MeanPool}(H^{(2)})$ \\
\textbf{12:} $\text{pred} \leftarrow \text{MLP}(h)$ \\
\textbf{13:} \textbf{return} $\arg\max(\text{pred})$
\end{flushleft}
\end{minipage}

\end{document}