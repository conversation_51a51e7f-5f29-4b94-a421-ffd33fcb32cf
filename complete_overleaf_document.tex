\documentclass[conference]{IEEEtran}

% 必需的包
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}

% 确保算法包正常工作
\renewcommand{\algorithmicrequire}{\textbf{Input:}}
\renewcommand{\algorithmicensure}{\textbf{Output:}}

\begin{document}

\title{Subgraph-based Graph Attention Network for Lightpath Interference Identification in Optical Networks}

\author{\IEEEauthorblockN{Author Name}
\IEEEauthorblockA{\textit{Department} \\
\textit{University}\\
City, Country \\
<EMAIL>}}

\maketitle

\begin{abstract}
Optical network management requires efficient identification of lightpath interference to maintain service quality. Traditional approaches either rely on computationally expensive full-graph processing or lack the ability to capture topological relationships. This paper presents a subgraph-based Graph Attention Network (GAT) that focuses on locally relevant network topology for interference prediction. Our method constructs smaller subgraphs (6-10 nodes) from the original network (14 nodes) while preserving critical interference relationships. Experimental validation shows our approach achieves 93.00\% test accuracy, significantly outperforming full-graph baselines (68.67\% for GCN, 64.00\% for GAT) while maintaining computational efficiency.
\end{abstract}

\begin{IEEEkeywords}
Optical networks, Graph neural networks, Lightpath interference, Subgraph construction
\end{IEEEkeywords}

\section{Introduction}
Optical networks carry increasing traffic volumes, making efficient Quality of Transmission (QoT) management crucial for network operators. When establishing new lightpaths, network planners must predict potential interference with existing services.

\section{Methodology}

\subsection{Problem Formulation}
Given an optical network represented as graph $G = (V, E)$ with $V$ nodes and $E$ links, we aim to predict whether a new lightpath $P_{new}$ will interfere with an existing target lightpath $P_{target}$.

\subsection{Subgraph Construction Strategy}
Rather than processing the entire network graph, we extract relevant subgraphs that contain the essential information for interference prediction. The subgraph construction algorithm is detailed in Algorithm~\ref{alg:subgraph_gat}.

% 算法放在这里
\begin{algorithm}[htbp]
\caption{Subgraph GAT for Interference Identification}
\label{alg:subgraph_gat}
\begin{algorithmic}[1]
\REQUIRE $G = (V, E)$, lightpaths $P_{new}, P_{target}$
\ENSURE Interference prediction $\{0, 1\}$

\STATE $V_{rel} \gets \text{nodes}(P_{new} \cup P_{target})$
\FOR{each $v \in V_{rel}$}
    \STATE $V_{rel} \gets V_{rel} \cup \text{neighbors}(v)[1:3]$ 
\ENDFOR
\STATE $G_{sub} \gets$ induced subgraph on $V_{rel}$

\FOR{each $v \in V_{rel}$}
    \STATE $F[v] \gets \text{generate\_features}(v, P_{new}, P_{target})$
\ENDFOR

\STATE $H^{(1)} \gets \text{GAT}(F, G_{sub})$
\STATE $H^{(2)} \gets \text{GAT}(H^{(1)}, G_{sub})$
\STATE $h \gets \text{MeanPool}(H^{(2)})$
\STATE $pred \gets \text{MLP}(h)$
\RETURN $\arg\max(pred)$
\end{algorithmic}
\end{algorithm}

The construction process begins by identifying all nodes involved in both lightpaths, then adds up to 3 one-hop neighbors for each path node.

\subsection{Multi-head Graph Attention Network}
The extracted subgraph and node features are processed by a two-layer GAT with 4 attention heads per layer. Each node is characterized by a 7-dimensional feature vector.

\section{Experimental Evaluation}

\subsection{Experimental Setup}
Experiments are conducted on a 14-node Japanese network topology with 29 bidirectional links. We generate 1000 lightpath interference scenarios with realistic class distribution.

\subsection{Performance Results}
Table~\ref{tab:performance} presents the experimental results comparing our subgraph GAT approach with two full-graph baselines.

\begin{table}[htbp]
\centering
\caption{Performance Comparison of Different Methods}
\label{tab:performance}
\begin{tabular}{lccc}
\hline
Method & Test Acc. (\%) & F1 Score (\%) & Params \\
\hline
\textbf{Subgraph GAT (Ours)} & \textbf{93.00} & \textbf{92.99} & 21,986 \\
Full Graph GCN & 68.67 & 55.91 & 1,282 \\
Full Graph GAT & 64.00 & 59.43 & 17,666 \\
\hline
\end{tabular}
\end{table}

Our subgraph-based approach significantly outperforms both full-graph baselines, achieving 93.00\% test accuracy.

\section{Conclusions}
This paper presents a subgraph-based GAT approach for lightpath interference identification in optical networks. By focusing on locally relevant topology, our method achieves 93.00\% test accuracy, representing a significant improvement over full-graph approaches.

The complete experimental code and results are available to ensure reproducibility of all reported findings.

\begin{thebibliography}{1}
\bibitem{b1} P. Poggiolini, ``The GN model of non-linear propagation in uncompensated coherent optical systems,'' \emph{J. Lightwave Technol.}, vol. 30, no. 24, pp. 3857--3879, 2012.
\end{thebibliography}

\end{document}