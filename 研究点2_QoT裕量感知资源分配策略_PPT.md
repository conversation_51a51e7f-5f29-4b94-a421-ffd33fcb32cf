# 基于深度强化学习的QoT裕量感知资源分配策略

---

## 🔴 技术挑战：
准确预测新光路对全网QoT影响面临以下关键挑战：

• **传统资源分配策略缺乏QoT感知能力**，采用固定裕量分配方式，计算复杂度为O(N³)，其中N为光路数量，无法满足大规模动态网络秒级响应需求。

• **多约束耦合优化难以求解**，路径选择、波长分配、功率控制三者相互耦合，传统启发式方法(如共享链路判断)过于简化，适用重要影响。

• **QoT裕量与网络容量存在根本性矛盾**，难以用解析模型描述，传统机器学习方法难以捕捉光网络的**图结构特性**。

---

## 🔴 研究动机：

• 基于上述挑战分析，一个关键洞察

  • **物理上**，光信号的QoT裕量需要集中在其传输路径及邻近区域

  • **大部分资源分配决策的已有光路受到的影响微乎其微**

  • **真正需要裕量调整的是一个相对较小的**"影响域"

• **智能识别这个动态影响域**，在子图上进行高精度的QoT裕量分配，融合物理先验知识提升预测可靠性

---

## 🔴 研究方案：

• 基于这一洞察，我们提出了**动态影响域的概念**：

  • **对于任意新业务请求**，存在一个包含其主要QoT影响范围的子域；

  • **该子域的规模远小于全网**，但包含了预测QoT裕量分配所需的关键信息。

• **智能识别这个动态影响域**，在子域上进行行精度的**QoT裕量感知资源分配**，融合物理先验知识提升分配可靠性

---

## 🔴 核心创新：

### 1. QoT裕量感知的状态空间设计
- **多维状态表示**：[网络拓扑, 光路状态, QoT裕量分布, 资源占用]
- **物理约束建模**：集成OSNR、串扰、非线性效应等物理模型
- **动态裕量跟踪**：实时监测各光路的QoT裕量变化

### 2. 深度强化学习决策框架
- **Actor-Critic架构**：策略网络输出资源分配动作，价值网络评估长期收益
- **多目标奖励函数**：平衡QoT保障、频谱效率、功率消耗三个目标
- **经验回放机制**：从历史决策中学习，提升决策质量

### 3. 分层决策机制
- **快速响应层**：毫秒级功率微调和波长切换
- **中期调整层**：分钟级路径重路由和负载均衡  
- **长期规划层**：小时级网络拓扑优化

---

## 🔴 预期贡献：

### 理论贡献
- **QoT裕量感知模型**：建立QoT裕量与网络容量的理论关系
- **多约束DRL框架**：解决光网络多约束耦合优化问题
- **动态适应机制**：实现网络状态变化的实时响应

### 技术贡献  
- **计算复杂度降低**：从O(N³)降低到O(K·log N)，K为影响域大小
- **决策速度提升**：响应时间从秒级提升到毫秒级
- **资源利用率提升**：相比固定裕量策略提升15-25%

### 应用价值
- **智能光网络**：支持5G/6G网络的动态资源分配需求
- **数据中心互联**：优化大规模数据中心间的光连接
- **工业互联网**：满足工业4.0对确定性网络的要求
